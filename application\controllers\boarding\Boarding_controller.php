<?php

/**
 * Name:    Oxygen
 * Author:  <PERSON><PERSON>
 *          <EMAIL>
 *
 * Created:  29 August 2022
 *
 * Description: Controller for Boarding Module. Entry point for Boarding Module
 *
 * Requirements: PHP5 or above
 *
 */

class Boarding_controller extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if (!$this->ion_auth->logged_in()) {
            redirect('auth/login', 'refresh');
        }

        $this->load->model('boarding/boarding_model');
    }

    //Landing function to show non-compliance menu
    public function index()
    {
        $site_url = site_url();

        if(!$this->authorization->isAuthorized('BOARDING.MODULE')){
            redirect('dashboard','refresh');
        }

        $data['master_data'] = array(
            [
                'title' => 'Campus Management',
                'sub_title' => 'View and add Room Type',
                'icon' => 'svg_icons/subjects.svg',
                'url' => $site_url . 'boarding/boarding_controller/boarding_management',
                'permission' => $this->authorization->isAuthorized('BOARDING.CAMPUS_MANAGEMENT'),
            ],
            [
                'title' => 'Room Type',
                'sub_title' => 'View and add Room Type',
                'icon' => 'svg_icons/subjects.svg',
                'url' => $site_url . 'boarding/boarding_controller/boarding_room_type',
                'permission' => $this->authorization->isAuthorized('BOARDING.ROOM_TYPE'),
            ],
            // [
            //     'title' => 'Rooms',
            //     'sub_title' => 'View and add Rooms',
            //     'icon' => 'svg_icons/subjects.svg',
            //     'url' => $site_url . 'boarding/boarding_controller/boarding_rooms',
            //     'permission' => $this->authorization->isAuthorized('DONATION.MODULE'),
            // ],
            // [
            //     'title' => 'Beds',
            //     'sub_title' => 'View and add Room Seats',
            //     'icon' => 'svg_icons/subjects.svg',
            //     'url' => $site_url . 'boarding/boarding_controller/boarding_room_seats',
            //     'permission' => $this->authorization->isAuthorized('DONATION.MODULE'),
            // ],
        );
        $data['master_data'] = checkTilePermissions($data['master_data']);

        $data['adminstration'] = array(
            [
                'title' => 'Manage Warden',
                'sub_title' => 'View and add Warden Allocation',
                'icon' => 'svg_icons/staff.svg',
                'url' => $site_url . 'boarding/boarding_controller/boarding_warden_allocation',
                'permission' => $this->authorization->isAuthorized('BOARDING.MANAGE_WARDEN'),
            ],
            [
                'title' => 'Manage Observation Categories',
                'sub_title' => 'View and add Observation Categories',
                'icon' => 'svg_icons/assestcategory.svg',
                'url' => $site_url . 'boarding/boarding_controller/manage_observations',
                'permission' => $this->authorization->isAuthorized('BOARDING.MANAGE_OBSERVATION_CATEGORIES'),
            ],
            [
                'title' => 'Manage Attendance Sessions',
                'sub_title' => 'Manage Sessions',
                'icon' => 'svg_icons/assestcategory.svg',
                'url' => $site_url . 'boarding/boarding_controller/manage_sessions',
                'permission' => $this->authorization->isAuthorized('BOARDING.MANAGE_ATTENDANCE_SESSIONS'),
            ]               
        );
        $data['adminstration'] = checkTilePermissions($data['adminstration']);

        $data['report_tiles'] = array(
            [
                'title' => 'Boarding Campus Report',
                'sub_title' => 'Boarding Campus Report',
                'icon' => 'svg_icons/assessment.svg',
                'url' => $site_url . 'boarding/boarding_controller',
                'permission' => $this->authorization->isAuthorized('BOARDING.BOARDING_CAMPUS_REPORT'),
            ],
            [
                'title' => 'Block Wise Report',
                'sub_title' => 'Block Wise Report',
                'icon' => 'svg_icons/assessment.svg',
                'url' => $site_url . 'boarding/boarding_controller',
                'permission' => $this->authorization->isAuthorized('BOARDING.BLOCK_WISE_REPORT'),
            ],
            [
                'title' => 'Bed Allotment Report',
                'sub_title' => 'Bed Allotment Report',
                'icon' => 'svg_icons/assessment.svg',
                'url' => $site_url . 'boarding/boarding_controller',
                'permission' => $this->authorization->isAuthorized('BOARDING.BED_ALLOTMENT_REPORT'),
            ],
            [
                'title' => 'Bed Vacancy Reports',
                'sub_title' => 'Bed Vacancy Reports',
                'icon' => 'svg_icons/assessment.svg',
                'url' => $site_url . 'boarding/boarding_controller',
                'permission' => $this->authorization->isAuthorized('BOARDING.BED_VACANCY_REPORT'),
            ],
            [
                'title' => 'Observation Report',
                'sub_title' => 'Observation Report',
                'icon' => 'svg_icons/assessment.svg',
                'url' => $site_url . 'boarding/boarding_controller',
                'permission' => $this->authorization->isAuthorized('BOARDING.OBSERVATION_REPORT'),
            ],
            [
                'title' => 'Attendance Report',
                'sub_title' => 'Attendance Report',
                'icon' => 'svg_icons/assessment.svg',
                'url' => $site_url . 'boarding/boarding_controller',
                'permission' => $this->authorization->isAuthorized('BOARDING.ATTENDANCE_REPORT'),
            ],
        );
        $data['report_tiles'] = checkTilePermissions($data['report_tiles']);

        $data['boarding'] = array(
            [
                'title' => 'Allocation',
                'sub_title' => 'View and Manage Wards',
                'icon' => 'svg_icons/subjects.svg',
                'url' => $site_url . 'boarding/boarding_controller/manage_wards',
                'permission' => $this->authorization->isAuthorized('BOARDING.ALLOCATION'),
            ],
            [
                'title' => 'My Observations',
                'sub_title' => 'View and add Observation',
                'icon' => 'svg_icons/subjects.svg',
                'url' => $site_url . 'boarding/boarding_controller/wards_observation',
                'permission' => $this->authorization->isAuthorized('BOARDING.MY_OBSERVATIONS'),
            ],
            [
                'title' => 'De-activate Observations',
                'sub_title' => 'View and deactivate Observation',
                'icon' => 'svg_icons/assestdiscardreport.svg',
                'url' => $site_url . 'boarding/boarding_controller/deactivate_observation',
                'permission' => $this->authorization->isAuthorized('BOARDING.DEACTIVATE_OBSERVATION'),
            ],
            [
                'title' => 'Take Attendence',
                'sub_title' => 'Take Attendence',
                'icon' => 'svg_icons/assestcategory.svg',
                'url' => $site_url . 'boarding/boarding_controller/manage_attendence_session',
                'permission' => $this->authorization->isAuthorized('BOARDING.TAKE_ATTENDENCE'),
            ],
        );
        $data['boarding'] = checkTilePermissions($data['boarding']);

        $data['main_content'] = 'boarding/dashboard';
        $this->load->view('inc/template', $data);
    }

    public function deactivate_observation()
    {
        $data['main_content'] = 'boarding/wards_observation/deactivate_ward_observation.php';
        $this->load->view('inc/template', $data);
    }

    public function manage_observations()
    {
        $data['main_content'] = 'boarding/wards_observation/manage_categories.php';
        $this->load->view('inc/template', $data);

    }

    public function manage_sessions(){
        $data['main_content'] = 'boarding/attendence_session/manage_session_page.php';
        $this->load->view('inc/template', $data);
    }

    public function manage_attendence_session(){
        $data['block_names'] = $this->boarding_model->get_boarding_block_data();
        $data['session_names'] = $this->boarding_model->get_session_names();
        $data['main_content'] = 'boarding/attendence_session/manage_attendence_session';

        $this->load->view('inc/template', $data);
    }

    public function wards_observation()
    {
        $resources = $this->settings->getSetting('resources');
        $fileSize = '5MB';
        $fileTypes = 'jpg, jpeg, png';
        if(!empty($resources) && isset($resources->resource_size)){
            $fileSize = $resources->resource_size ? $resources->resource_size : '5MB';
        }
        $data['fileSize'] = $fileSize;
        $data['fileTypes'] = $fileTypes;
        $data['grades'] = $this->boarding_model->getClassNames();
        $data['blocks'] = $this->boarding_model->get_boarding_block_data();
        $data['observation_category'] = $this->boarding_model->get_observation_category();
        $data['main_content'] = 'boarding/wards_observation/my_observation.php';
        $this->load->view('inc/template', $data);
    }

    public function get_student()
    {
        // echo "<pre>";
        // print_r($_POST);
        // die();
        $grade_id = $this->input->post('grade_id');
        $block_id = $this->input->post('block_id');

        $this->load->model('boarding_model');

        $students = array();

        if (!empty($grade_id)) {
            $students = $this->boarding_model->get_students_by_grade($grade_id);
        } elseif (!empty($block_id)) {
            $students = $this->boarding_model->get_students_by_block($block_id);
        }

        echo json_encode($students);
    }

    public function manage_wards()
    {
        $data['room_types'] = $this->boarding_model->get_boarding_room_type_data();
        $data['blocks'] = $this->boarding_model->get_boarding_block_data();
        $data['beds'] = $this->boarding_model->get_boarding_seats_data();
        $data['grades'] = $this->boarding_model->getClassNames();

        $data['main_content'] = 'boarding/manage_wards/manage_wards_dashboard.php';
        $this->load->view('inc/template', $data);
    }

    public function boarding_management()
    {
        $data['wardens'] = $this->boarding_model->get_boarding_warden_active();
        $data['room_types'] = $this->boarding_model->get_boarding_room_type_data();
        $data['main_content'] = 'boarding/boarding_management/boarding_management.php';
        $this->load->view('inc/template', $data);
    }

    public function boarding_warden_allocation()
    {
        $data['main_content'] = 'boarding/boarding_warden/boarding_warden_allocation';
        $this->load->view('inc/template', $data);
    }

    public function add_boarding_warden()
    {
        $result = $this->boarding_model->add_boarding_warden($_POST);
        if ($result) {
            $this->session->set_flashdata("flashSuccess", "New Warden Added Successfully");
        } else {
            $this->session->set_flashdata("flashError", "Error in Adding Warden");
        }
        redirect("boarding/boarding_controller/boarding_warden_allocation");
    }

    public function get_boarding_warden()
    {
        $result = $this->boarding_model->get_boarding_warden($_POST);
        echo json_encode($result);
    }

    public function status_update()
    {
        $result = $this->boarding_model->status_update($_POST);
        echo $result;
    }

    public function update_boarding_warden()
    {
        $result = $this->boarding_model->update_boarding_warden($_POST);
        echo $result;
    }

    // public function boarding_block()
    // {
    //     $data['main_content'] = 'boarding/boarding_block/boarding_block.php';
    //     $this->load->view('inc/template', $data);
    // }

    // public function boarding_rooms()
    // {
    //     $data['main_content'] = 'boarding/boarding_rooms/boarding_rooms.php';
    //     $this->load->view('inc/template', $data);
    // }

    public function boarding_room_type()
    {
        $data['main_content'] = 'boarding/boarding_room_type/boarding_room_type.php';
        $this->load->view('inc/template', $data);
    }

    // public function boarding_room_seats()
    // {
    //     $data['main_content'] = 'boarding/boarding_room_seats/boarding_room_seats.php';
    //     $this->load->view('inc/template', $data);
    // }
    // public function boarding_block_allocation()
    // {
    //     $data['main_content'] = 'boarding/boarding_block_allocation/boarding_block_allocation.php';
    //     $this->load->view('inc/template', $data);
    // }

    public function boarding_student_allocation()
    {
        $data['main_content'] = 'boarding/boarding_student_allocation/boarding_student_allocation.php';
        $this->load->view('inc/template', $data);
    }

    public function add_boarding_blocks()
    {
        $result = $this->boarding_model->add_boarding_blocks($_POST);
        echo $result;
    }

    public function get_boarding_block_data()
    {
        $result = $this->boarding_model->get_boarding_block_data();
        echo json_encode($result);
    }

    public function get_boarding_block_data_by_id()
    {
        $result = $this->boarding_model->get_boarding_block_data_by_id($_POST);
        echo json_encode(array('result' => $result));
    }

    public function status_update_block()
    {
        $result = $this->boarding_model->status_update_block($_POST);
        echo $result;
    }

    public function update_boarding_block()
    {

        $result = $this->boarding_model->update_boarding_block($_POST);
        echo $result;
    }

    public function assign_boarding_warden()
    {
        $result = $this->boarding_model->assign_boarding_warden($_POST);
        echo $result;
    }

    public function get_boarding_warden_active()
    {
        $result = $this->boarding_model->get_boarding_warden_active();
        echo json_encode($result);
    }

    public function get_rooms_by_status()
    {
        $result = $this->boarding_model->get_rooms_by_status($_POST);
        echo json_encode($result);
    }

    public function get_student_allocation_data()
    {
        $result = $this->boarding_model->get_student_allocation_data();
        echo json_encode($result);
    }

    public function add_boarding_room()
    {
        $result = $this->boarding_model->add_boarding_room($_POST);
        echo json_encode($result);
    }

    public function update_boarding_room()
    {
        $result = $this->boarding_model->update_boarding_room($_POST);
        echo json_encode($result);
    }

    public function get_boarding_rooms_data()
    {
        $result = $this->boarding_model->get_boarding_rooms_data($_POST);
        echo json_encode($result);
    }

    public function get_rooms_list()
    {
        $result = $this->boarding_model->get_rooms_list($_POST);
        echo json_encode($result);
    }

    public function get_seat_list()
    {
        $result = $this->boarding_model->get_seat_list($_POST);
        echo json_encode($result);
    }

    public function get_class_list()
    {
        $result = $this->boarding_model->getClassNames();
        echo json_encode($result);
    }

    public function get_student_list()
    {
        $section_id = $_POST['section_id'];
        $result = $this->boarding_model->get_student_list($section_id);
        echo json_encode($result);
    }

    public function add_boarding_room_seats()
    {
        $result = $this->boarding_model->add_boarding_room_seats($_POST);
        echo $result;
    }

    public function update_boarding_room_seats()
    {
        $result = $this->boarding_model->update_boarding_room_seats($_POST);
        echo $result;
    }

    public function get_boarding_seats_data()
    {
        $result = $this->boarding_model->get_boarding_seats_data();
        echo json_encode($result);
    }

    public function seat_status_update()
    {
        $result = $this->boarding_model->seat_status_update($_POST);
        echo $result;
    }

    public function add_boarding_room_type()
    {
        $result = $this->boarding_model->add_boarding_room_type($_POST);
        if ($result) {
            $this->session->set_flashdata("flashSuccess", "New Room Type Inserted Successfully");
        } else {
            $this->session->set_flashdata("flashError", "Error in Adding Block");
        }
        redirect("boarding/boarding_controller/boarding_room_type");
    }

    public function get_boarding_room_type_data()
    {
        $result = $this->boarding_model->get_boarding_room_type_data();
        echo json_encode($result);
    }

    public function add_student_allocation()
    {
        $result = $this->boarding_model->add_student_allocation($_POST);
        if ($result) {
            $this->session->set_flashdata("flashSuccess", "Student Allocated Successfully");
        } else {
            $this->session->set_flashdata("flashError", "Error in Allocating Student");
        }
        redirect("boarding/boarding_controller/manage_wards");
    }

    public function vacate_student()
    {
        $result = $this->boarding_model->vacate_student($_POST);

        echo $result;
    }

    public function reallocate_student()
    {
        $result = $this->boarding_model->reallocate_student($_POST);
        echo $result;
    }

    public function get_student_allocation_name()
    {
        $result = $this->boarding_model->get_student_allocation_name($_POST);
        // print_r($result);
        echo json_encode($result);
    }

    public function add_observation_category()
    {
        $result = $this->boarding_model->add_observation_category($_POST);
        echo $result;
    }

    public function get_observation_category()
    {
        $result = $this->boarding_model->get_observation_category();
        echo json_encode($result);
    }

    public function update_observation_category()
    {
        $result = $this->boarding_model->update_observation_category($_POST);
        echo json_encode($result);
    }

    public function delete_observation_category()
    {
        $result = $this->boarding_model->delete_observation_category($_POST);
        echo json_encode($result);
    }

    public function add_ward_observation() {
        // echo "<pre>";
        // print_r($_POST);
        // die();
        
        $input = $this->input->post();
        $file_input_url = isset($input['file_input']) ? $input['file_input'] : null;

        foreach ($input['student_name'] as $student_id) {
            $observation_data = array(
                'student_id' => $student_id,
                'category_id' => $input['category_id'],
                'observed_on' => $input['date'],
                'observation' => $input['observation'],
                'action_taken' => $input['action_taken'],
                'date' => date('Y-m-d H:i:s', strtotime($input['date'])),
                'file_input' => $file_input_url,
            );
            $success = $this->boarding_model->add_ward_observation($observation_data);
        }

        echo json_encode($success);
    }

    public function get_ward_observation_data()
    {
        // echo "<pre>";
        // print_r($_POST);
        // die();

        // $class_section_id = $_POST['class_section_id'];
        $from_date = date('Y-m-d', strtotime($_POST['from_date']));
        $to_date = date('Y-m-d', strtotime($_POST['to_date']));

        $observation_data = $this->boarding_model->get_ward_observation_data($from_date, $to_date);

        $this->load->library('filemanager');

        foreach ($observation_data as $observation) {
            if ($observation->file_input) {
                $observation->file_input = $this->filemanager->getFilePath($observation->file_input);
            }
        }

        $response_data = array(
            'observation_data' => $observation_data,
        );

        echo json_encode($response_data);
    }

    public function deactivate_ward_observation()
    {
        $result = $this->boarding_model->deactivate_ward_observation($_POST);
        echo json_encode($result);
    }

    public function get_student_names_block_wise(){
        $result = $this->boarding_model->get_student_names_block_wise($_POST['block_id'],date("Y-m-d", strtotime($_POST['date'])),$_POST['session_id']);
        echo json_encode($result);
    }

    public function add_session_names(){
       echo  $this->boarding_model->add_session_names($_POST['session_name']);
    }

    public function get_session_names(){
        $result = $this->boarding_model->get_session_names();
        echo json_encode($result);
    }

    public function submit_boarding_attendence(){
        $temp_arr = [];
        foreach($_POST['student_ids'] as $key =>$val){
            if(!empty($_POST['attendence_status']) && in_array($val,$_POST['attendence_status'])){
                $temp_arr[$val] = 1;
            }else{
                $temp_arr[$val] = 0;
            }
        }
        echo $this->boarding_model->submit_boarding_attendence($_POST,$temp_arr);
    }

    public function update_boarding_attendence(){
        $temp_arr = [];
        foreach($_POST['stud_attendence_id'] as $key =>$val){
            if(!empty($_POST['stud_attendence_id_edit']) && in_array($val,$_POST['stud_attendence_id_edit'])){
                $temp_arr[$val] = 1;
            }else{
                $temp_arr[$val] = 0;
            }
        }
        echo $this->boarding_model->update_boarding_attendence($temp_arr);
    }
}
