<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Laundry_controller extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        $this->load->model('boarding/laundry_model');
        $this->load->library('ion_auth');
        $this->load->library('authorization');
        $this->load->helper('laundry');

        if (!$this->ion_auth->logged_in()) {
            redirect('auth/login', 'refresh');
        }
    }

    public function Kolkata_datetime(){
        $timezone = new DateTimeZone("Asia/Kolkata" );
        $date = new DateTime();
        $date->setTimezone($timezone );
        $dtobj = $date->format('Y-m-d H:i:s');
        return $dtobj;
    }

    /**
     * Main laundry dashboard
     */
    public function index()
    {
        $site_url = site_url();

        if (!$this->authorization->isAuthorized('BOARDING.LAUNDRY_MANAGEMENT')) {
            redirect('dashboard', 'refresh');
        }

        $data['title'] = 'Laundry Management';

        $data['laundry_tiles'] = array(
            [
                'title' => 'New Transaction',
                'sub_title' => 'Create new laundry transaction',
                'icon' => 'svg_icons/staff.svg',
                'url' => $site_url . 'boarding/laundry_controller/add_transaction',
                'permission' => $this->authorization->isAuthorized('BOARDING.LAUNDRY_NEW_TRANSACTION'),
            ],
            [
                'title' => 'All Transactions',
                'sub_title' => 'View and manage all transactions',
                'icon' => 'svg_icons/assestcategory.svg',
                'url' => $site_url . 'boarding/laundry_controller/transactions',
                'permission' => $this->authorization->isAuthorized('BOARDING.LAUNDRY_ALL_TRANSACTIONS'),
            ],
            [
                'title' => 'Item Categories',
                'sub_title' => 'Manage laundry item categories',
                'icon' => 'svg_icons/assestcategory.svg',
                'url' => $site_url . 'boarding/laundry_controller/categories',
                'permission' => $this->authorization->isAuthorized('BOARDING.LAUNDRY_ITEM_CATEGORIES'),
            ],
            [
                'title' => 'Pending Returns',
                'sub_title' => 'Items pending return',
                'icon' => 'svg_icons/subjects.svg',
                'url' => $site_url . 'boarding/laundry_controller/transactions?filter=pending',
                'permission' => $this->authorization->isAuthorized('BOARDING.LAUNDRY_PENDING_RETURNS'),
            ]
        );

        // ✅ Only keep authorized tiles
        $data['laundry_tiles'] = checkTilePermissions($data['laundry_tiles']);

        // Extra data for the view (if still needed)
        $data['categories'] = $this->laundry_model->get_categories();
        $data['transactions'] = $this->laundry_model->get_recent_transactions();

        // Load template
        $data['main_content'] = 'boarding/laundry/index';
        $this->load->view('inc/template', $data);
    }


    /**
     * Laundry item categories management
     */
    public function categories()
    {
        if (!$this->authorization->isAuthorized('BOARDING.LAUNDRY_CATEGORIES')) {
            redirect('dashboard', 'refresh');
        }

        $data['title'] = 'Laundry Item Categories';
        $data['categories'] = $this->laundry_model->get_categories();

        // $this->load->view('boarding/laundry/categories', $data);
        $data['main_content'] = 'boarding/laundry/categories';
        $this->load->view('inc/template', $data);
    }

    /**
     * Add new laundry category
     */
    public function add_category()
    {
        if (!$this->authorization->isAuthorized('BOARDING.LAUNDRY_CATEGORIES')) {
            redirect('dashboard', 'refresh');
        }

        if ($_POST) {
            $data = array(
                'item_name' => $this->input->post('item_name'),
                'status' => 1,
                'created_by' => $this->ion_auth->user()->row()->id,
                'created_on' => date('Y-m-d H:i:s')
            );

            if ($this->laundry_model->add_category($data)) {
                $this->session->set_flashdata('success', 'Category added successfully');
            } else {
                $this->session->set_flashdata('error', 'Failed to add category');
            }
            redirect('boarding/laundry_controller/categories');
        }

        $data['title'] = 'Add Laundry Category';
        // $this->load->view('boarding/laundry/add_category', $data);
        $data['main_content'] = 'boarding/laundry/add_category';
        $this->load->view('inc/template', $data);
        
    }

    /**
     * Edit laundry category
     */
    public function edit_category($id)
    {
        if ($_POST) {
            $data = array(
                'item_name' => $this->input->post('item_name'),
                'status' => $this->input->post('status'),
                'updated_by' => $this->ion_auth->user()->row()->id,
                'updated_on' => date('Y-m-d H:i:s')
            );

            if ($this->laundry_model->update_category($id, $data)) {
                $this->session->set_flashdata('success', 'Category updated successfully');
            } else {
                $this->session->set_flashdata('error', 'Failed to update category');
            }
            redirect('boarding/laundry_controller/categories');
        }

        $data['title'] = 'Edit Laundry Category';
        $data['category'] = $this->laundry_model->get_category($id);
        // $this->load->view('boarding/laundry/edit_category', $data);
        $data['main_content'] = 'boarding/laundry/edit_category';
        $this->load->view('inc/template', $data);
    }

    /**
     * Laundry transactions management
     */
    public function transactions()
    {
        if (!$this->authorization->isAuthorized('BOARDING.LAUNDRY_TRANSACTIONS')) {
            redirect('dashboard', 'refresh');
        }

        $data['title'] = 'Laundry Transactions';
        $data['transactions'] = $this->laundry_model->get_transactions();

        // $this->load->view('boarding/laundry/transactions', $data);
        $data['main_content'] = 'boarding/laundry/transactions';
        $this->load->view('inc/template', $data);
    }

    public function get_transactions()
    {
        $this->db->select('
        lst.*,
        s.first_name,
        s.last_name,
        s.admission_no,
        b.block_name,
        u1.username AS issued_by_name,
        u2.username AS received_by_name
    ');
        $this->db->from('laundry_student_txn lst');
        $this->db->join('student_admission s', 's.id = lst.student_id', 'left');
        $this->db->join('boarding_blocks b', 'b.id = lst.block_id', 'left');
        $this->db->join('users u1', 'u1.id = lst.issued_by', 'left');
        $this->db->join('users u2', 'u2.id = lst.received_by', 'left');

        // Optional: Apply filters from GET request
        if ($this->input->get('student_id')) {
            $this->db->where('lst.student_id', $this->input->get('student_id'));
        }
        if ($this->input->get('block_id')) {
            $this->db->where('lst.block_id', $this->input->get('block_id'));
        }
        if ($this->input->get('payment_status')) {
            $this->db->where('lst.payment_status', $this->input->get('payment_status'));
        }
        if ($this->input->get('date_from')) {
            $this->db->where('DATE(lst.created_on) >=', $this->input->get('date_from'));
        }
        if ($this->input->get('date_to')) {
            $this->db->where('DATE(lst.created_on) <=', $this->input->get('date_to'));
        }

        $this->db->order_by('lst.created_on', 'DESC');

        return $this->db->get()->result();
    }

    /**
     * Add new laundry transaction
     */
    public function add_transaction()
    {
        if (!$this->authorization->isAuthorized('BOARDING.LAUNDRY_TRANSACTIONS')) {
            redirect('dashboard', 'refresh');
        }

        if ($_POST) {
            $transaction_data = array(
                'student_id' => $this->input->post('student_id'),
                'block_id' => $this->input->post('block_id'),
                'issued_on' => $this->input->post('issued_on'),
                // 'issued_by' => $this->ion_auth->user()->row()->id,
                'issued_by' => $this->authorization->getAvatarStakeHolderId(),
                'status' => 1,
                'billing_amount' => $this->input->post('billing_amount'),
                'payment_status' => $this->input->post('payment_status'),
                'created_on' => date('Y-m-d H:i:s')
            );

            $transaction_id = $this->laundry_model->add_transaction($transaction_data);

            if ($transaction_id) {
                // Add transaction items
                $items = $this->input->post('items');
                if ($items) {
                    foreach ($items as $item) {
                        $item_data = array(
                            'txn_id' => $transaction_id,
                            'category_id' => $item['category_id'],
                            'count_out' => $item['count_out'],
                            'count_in' => $item['count_in'],
                            'discrepancy_flag' => 0,
                            'remarks' => $item['remarks']
                        );
                        $this->laundry_model->add_transaction_item($item_data);
                    }
                }

                $this->session->set_flashdata('success', 'Transaction added successfully');
            } else {
                $this->session->set_flashdata('error', 'Failed to add transaction');
            }
            redirect('boarding/laundry_controller/transactions');
        }

        $data['title'] = 'Add Laundry Transaction';
        $data['categories'] = $this->laundry_model->get_categories();
        $data['students'] = $this->laundry_model->get_students();
        $data['blocks'] = $this->laundry_model->get_blocks();

        // $this->load->view('boarding/laundry/add_transaction', $data);
        $data['main_content'] = 'boarding/laundry/add_transaction';
        $this->load->view('inc/template', $data);
    }

    /**
     * View transaction details
     */
    public function view_transaction($id)
    {
        $data['title'] = 'Transaction Details';
        $data['transaction'] = $this->laundry_model->get_transaction($id);
        $data['transaction_items'] = $this->laundry_model->get_transaction_items($id);

        // $this->load->view('boarding/laundry/view_transaction', $data);
        $data['main_content'] = 'boarding/laundry/view_transaction';
        $this->load->view('inc/template', $data);
    }

    /**
     * Return laundry items
     */
    public function return_items($id)
    {
        if ($_POST) {
            $items = $this->input->post('items');
            $returned_on = $this->input->post('returned_on');
            $received_by = $this->ion_auth->user()->row()->id;

            foreach ($items as $item_id => $item_data) {
                $update_data = array(
                    'count_in' => $item_data['count_in'],
                    'discrepancy_flag' => $item_data['discrepancy_flag'],
                    'remarks' => $item_data['remarks']
                );
                $this->laundry_model->update_transaction_item($item_id, $update_data);
            }

            // Update transaction
            $transaction_data = array(
                'returned_on' => $returned_on,
                'received_by' => $received_by,
                'updated_on' => date('Y-m-d H:i:s')
            );
            $this->laundry_model->update_transaction($id, $transaction_data);

            $this->session->set_flashdata('success', 'Items returned successfully');
            redirect('boarding/laundry_controller/view_transaction/' . $id);
        }

        $data['title'] = 'Return Laundry Items';
        $data['transaction'] = $this->laundry_model->get_transaction($id);
        $data['transaction_items'] = $this->laundry_model->get_transaction_items($id);

        // $this->load->view('boarding/laundry/return_items', $data);
        $data['main_content'] = 'boarding/laundry/return_items';
        $this->load->view('inc/template', $data);
    }

    /**
     * Get students by block (AJAX)
     */
    public function get_students_by_block()
    {
        $block_id = $this->input->post('block_id');
        $students = $this->laundry_model->get_students_by_block($block_id);
        echo json_encode($students);
    }

    /**
     * Delete category
     */
    public function delete_category($id)
    {
        if ($this->laundry_model->delete_category($id)) {
            $this->session->set_flashdata('success', 'Category deleted successfully');
        } else {
            $this->session->set_flashdata('error', 'Failed to delete category');
        }
        redirect('boarding/laundry_controller/categories');
    }
}
